#!/bin/bash

# VPS数据上报测试脚本
# 用于验证修正后的数据收集和上报功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 测试函数
test_function() {
    local func_name=$1
    local description=$2

    print_message "$BLUE" "测试: $description"

    # 简单测试：直接调用函数
    if declare -f "$func_name" >/dev/null 2>&1; then
        local result=$($func_name 2>/dev/null || echo "ERROR")

        if [[ "$result" != "ERROR" && -n "$result" ]]; then
            if [[ "$func_name" == "get_uptime" ]]; then
                # uptime返回数字
                if [[ "$result" =~ ^[0-9]+$ ]]; then
                    print_message "$GREEN" "✓ $func_name 返回有效数字: $result"
                else
                    print_message "$RED" "✗ $func_name 返回无效数据: $result"
                fi
            else
                # 其他函数返回JSON
                if [[ "$result" =~ ^\{.*\}$ ]]; then
                    print_message "$GREEN" "✓ $func_name 返回有效JSON"
                    print_message "$BLUE" "  数据: ${result:0:100}..."
                else
                    print_message "$RED" "✗ $func_name 返回无效数据: $result"
                fi
            fi
        else
            print_message "$RED" "✗ $func_name 执行失败"
        fi
    else
        print_message "$YELLOW" "? $func_name 函数未定义"
    fi

    echo
}

# 测试数值清理函数
test_sanitize_functions() {
    print_message "$BLUE" "测试数值清理函数"

    # 定义简化的测试函数
    sanitize_number() {
        local value="$1"
        local default_value="${2:-0}"
        value=$(echo "$value" | sed 's/[^0-9.]//g')
        if [[ "$value" =~ ^[0-9]+\.?[0-9]*$ ]] || [[ "$value" =~ ^[0-9]*\.[0-9]+$ ]]; then
            echo "$value"
        else
            echo "$default_value"
        fi
    }
    
    # 测试sanitize_number
    local test_cases=(
        "123.45:123.45"
        "abc123.45def:123.45"
        ".5:0.5"
        "5.:5.0"
        "invalid:0"
        "":0
    )
    
    for case in "${test_cases[@]}"; do
        local input="${case%:*}"
        local expected="${case#*:}"
        local result=$(sanitize_number "$input")
        
        if [[ "$result" == "$expected" ]]; then
            print_message "$GREEN" "✓ sanitize_number('$input') = '$result'"
        else
            print_message "$RED" "✗ sanitize_number('$input') = '$result', expected '$expected'"
        fi
    done
    
    echo
}

# 测试JSON清理函数
test_json_cleaning() {
    print_message "$BLUE" "测试JSON清理函数"

    # 定义简化的测试函数
    clean_json_string() {
        local input="$1"
        echo "$input" | tr -d '\000-\037' | tr -d '\177-\377'
    }
    
    local test_input='{"test": "value with\x00control\x1fchars"}'
    local result=$(clean_json_string "$test_input")
    
    if [[ "$result" == '{"test": "value withcontrolchars"}' ]]; then
        print_message "$GREEN" "✓ JSON清理功能正常"
    else
        print_message "$YELLOW" "? JSON清理结果: $result"
    fi
    
    echo
}

# 主测试函数
main() {
    print_message "$BLUE" "=== VPS数据上报功能测试 ==="
    echo
    
    # 检查依赖
    if ! command -v python3 >/dev/null 2>&1; then
        print_message "$YELLOW" "警告: python3未安装，跳过JSON验证测试"
    fi
    
    # 设置必要的环境变量
    export OS=$(uname -s)
    export CONTAINER_ENV="false"
    
    # 定义必要的函数
    command_exists() {
        command -v "$1" >/dev/null 2>&1
    }
    
    # 测试数值清理函数
    test_sanitize_functions
    
    # 测试JSON清理函数
    test_json_cleaning
    
    # 测试各个数据收集函数
    if command_exists bc; then
        test_function "get_cpu_usage" "CPU使用率收集"
    else
        print_message "$YELLOW" "跳过CPU测试 - bc命令不可用"
    fi
    
    test_function "get_memory_usage" "内存使用情况收集"
    test_function "get_disk_usage" "磁盘使用情况收集"
    test_function "get_network_usage" "网络使用情况收集"
    test_function "get_uptime" "系统运行时间收集"
    
    print_message "$GREEN" "=== 测试完成 ==="
}

# 运行测试
main "$@"
