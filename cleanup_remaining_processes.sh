#!/bin/bash

echo "=== 清理遗留监控进程 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

echo "1. 查找所有可能的监控相关进程："
echo "=================================="

# 查找所有可能相关的进程
echo "搜索关键词: monitor, vps, cf-, api/report"
ps aux | grep -E "(monitor|vps|cf-|api/report)" | grep -v grep | grep -v "cleanup_remaining" | while read -r line; do
    pid=$(echo "$line" | awk '{print $2}')
    user=$(echo "$line" | awk '{print $1}')
    cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
    echo "PID: $pid | User: $user | Command: $cmd"
done

echo
echo "2. 查找具体的监控脚本进程："
echo "=================================="

# 更精确的搜索
monitor_pids=()
while read -r line; do
    if [[ -n "$line" ]]; then
        pid=$(echo "$line" | awk '{print $2}')
        cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
        
        # 检查是否是真正的监控进程
        if echo "$cmd" | grep -qE "(cf-vps-monitor|vps.*monitor|monitor\.sh)" && \
           ! echo "$cmd" | grep -q "cleanup_remaining"; then
            monitor_pids+=("$pid")
            echo "发现监控进程: PID $pid - $cmd"
        fi
    fi
done < <(ps aux | grep -E "(monitor|vps|cf-)" | grep -v grep | grep -v "cleanup_remaining")

echo
echo "3. 查找可能的后台服务进程："
echo "=================================="

# 查找可能的systemd服务或后台进程
if command -v systemctl >/dev/null 2>&1; then
    echo "检查systemd服务..."
    systemctl --user list-units --all 2>/dev/null | grep -E "(monitor|vps)" || echo "没有发现用户级服务"
    systemctl list-units --all 2>/dev/null | grep -E "(monitor|vps)" || echo "没有发现系统级服务"
fi

echo
echo "4. 查找网络连接："
echo "=================================="

# 查找可能的网络连接
if command -v netstat >/dev/null 2>&1; then
    echo "检查网络连接..."
    netstat -tulpn 2>/dev/null | grep -E "(monitor|api)" || echo "没有发现相关网络连接"
fi

echo
echo "5. 清理选项："
echo "=================================="

if [[ ${#monitor_pids[@]} -gt 0 ]]; then
    print_status "$YELLOW" "发现 ${#monitor_pids[@]} 个监控进程需要清理"
    
    echo "进程列表："
    for pid in "${monitor_pids[@]}"; do
        cmd=$(ps -p "$pid" -o cmd= 2>/dev/null || echo "进程已退出")
        echo "  PID $pid: $cmd"
    done
    
    echo
    read -p "是否要强制终止这些进程？(y/N): " confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        print_status "$BLUE" "开始清理进程..."
        
        for pid in "${monitor_pids[@]}"; do
            if kill -0 "$pid" 2>/dev/null; then
                print_status "$BLUE" "终止进程: $pid"
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done
        
        sleep 2
        
        # 强制终止仍在运行的进程
        for pid in "${monitor_pids[@]}"; do
            if kill -0 "$pid" 2>/dev/null; then
                print_status "$BLUE" "强制终止进程: $pid"
                kill -KILL "$pid" 2>/dev/null || true
            fi
        done
        
        sleep 1
        
        # 最终检查
        remaining=0
        for pid in "${monitor_pids[@]}"; do
            if kill -0 "$pid" 2>/dev/null; then
                remaining=$((remaining + 1))
            fi
        done
        
        if [[ $remaining -eq 0 ]]; then
            print_status "$GREEN" "✓ 所有进程已成功清理"
        else
            print_status "$RED" "✗ 仍有 $remaining 个进程无法终止"
        fi
    else
        print_status "$BLUE" "跳过进程清理"
    fi
else
    print_status "$GREEN" "✓ 没有发现需要清理的监控进程"
fi

echo
echo "6. 最终验证："
echo "=================================="

final_check=$(ps aux | grep -E "(cf-vps-monitor|vps.*monitor|monitor\.sh)" | grep -v grep | grep -v "cleanup_remaining" | wc -l)
if [[ $final_check -eq 0 ]]; then
    print_status "$GREEN" "✓ 系统已完全清理，没有遗留的监控进程"
else
    print_status "$YELLOW" "⚠ 仍有 $final_check 个相关进程在运行："
    ps aux | grep -E "(cf-vps-monitor|vps.*monitor|monitor\.sh)" | grep -v grep | grep -v "cleanup_remaining"
fi

echo
echo "7. 建议："
echo "=================================="
echo "如果仍有遗留进程："
echo "1. 检查进程是否是其他用户启动的"
echo "2. 检查是否有systemd服务仍在运行"
echo "3. 考虑重启系统以完全清理"
echo "4. 手动终止特定进程: kill -9 <PID>"

echo
print_status "$GREEN" "清理检查完成！"
