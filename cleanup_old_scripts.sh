#!/bin/bash

echo "=== VPS监控脚本彻底清理工具 ==="
echo

# 1. 查找所有可能的监控进程
echo "1. 查找运行中的监控进程："
echo "--- 查找cf-vps-monitor相关进程 ---"
ps aux | grep -E "(cf-vps-monitor|monitor\.sh|vps.*monitor)" | grep -v grep

echo
echo "--- 查找curl上报进程 ---"
ps aux | grep -E "curl.*api/report" | grep -v grep

echo
echo "--- 查找可能的监控脚本进程 ---"
ps aux | grep -E "(bash.*monitor|sh.*monitor)" | grep -v grep

echo
echo "2. 查找systemd服务："
systemctl list-units --all | grep -E "(cf-vps|monitor|vps)" || echo "没有找到相关systemd服务"

echo
echo "3. 查找cron任务："
crontab -l 2>/dev/null | grep -E "(monitor|vps|cf-)" || echo "没有找到相关cron任务"

echo
echo "4. 查找可能的脚本文件："
echo "--- 查找/usr/local/bin中的脚本 ---"
find /usr/local/bin -name "*monitor*" -o -name "*vps*" 2>/dev/null || echo "没有找到"

echo "--- 查找/opt中的脚本 ---"
find /opt -name "*monitor*" -o -name "*vps*" 2>/dev/null || echo "没有找到"

echo "--- 查找/home中的脚本 ---"
find /home -name "*monitor*" -o -name "*vps*" 2>/dev/null || echo "没有找到"

echo "--- 查找/root中的脚本 ---"
find /root -name "*monitor*" -o -name "*vps*" 2>/dev/null || echo "没有找到"

echo
echo "5. 查找配置目录："
echo "--- 查找.cf-vps-monitor目录 ---"
find /home -name ".cf-vps-monitor" -type d 2>/dev/null || echo "没有找到"
find /root -name ".cf-vps-monitor" -type d 2>/dev/null || echo "没有找到"

echo
echo "6. 查找临时文件："
echo "--- 查找/tmp中的监控相关文件 ---"
find /tmp -name "*monitor*" -o -name "*vps*" 2>/dev/null || echo "没有找到"

echo
echo "=== 清理建议 ==="
echo "如果发现了旧的进程或文件，请执行以下命令进行清理："
echo

# 生成清理命令
echo "# 停止所有监控进程"
echo "pkill -f 'cf-vps-monitor'"
echo "pkill -f 'monitor.sh'"
echo "pkill -f 'api/report'"

echo
echo "# 停止systemd服务（如果存在）"
echo "sudo systemctl stop cf-vps-monitor 2>/dev/null || true"
echo "sudo systemctl disable cf-vps-monitor 2>/dev/null || true"
echo "sudo rm -f /etc/systemd/system/cf-vps-monitor.service"
echo "sudo systemctl daemon-reload"

echo
echo "# 清理配置目录"
echo "rm -rf ~/.cf-vps-monitor"
echo "sudo rm -rf /root/.cf-vps-monitor"

echo
echo "# 清理可能的脚本文件"
echo "sudo rm -f /usr/local/bin/*monitor*"
echo "sudo rm -f /opt/*monitor*"

echo
echo "# 清理临时文件"
echo "rm -f /tmp/*monitor*"
echo "rm -f /tmp/vps_monitor_*"

echo
echo "# 清理cron任务（如果有）"
echo "crontab -r  # 注意：这会删除所有cron任务，请谨慎使用"

echo
echo "=== 自动清理选项 ==="
read -p "是否要自动执行清理？(y/N): " auto_clean

if [[ "$auto_clean" =~ ^[Yy]$ ]]; then
    echo "开始自动清理..."
    
    # 停止进程
    echo "停止监控进程..."
    pkill -f 'cf-vps-monitor' 2>/dev/null || true
    pkill -f 'monitor.sh' 2>/dev/null || true
    pkill -f 'api/report' 2>/dev/null || true
    
    # 停止服务
    echo "停止systemd服务..."
    systemctl stop cf-vps-monitor 2>/dev/null || true
    systemctl disable cf-vps-monitor 2>/dev/null || true
    rm -f /etc/systemd/system/cf-vps-monitor.service 2>/dev/null || true
    systemctl daemon-reload 2>/dev/null || true
    
    # 清理目录
    echo "清理配置目录..."
    rm -rf ~/.cf-vps-monitor 2>/dev/null || true
    rm -rf /root/.cf-vps-monitor 2>/dev/null || true
    
    # 清理临时文件
    echo "清理临时文件..."
    rm -f /tmp/*monitor* 2>/dev/null || true
    rm -f /tmp/vps_monitor_* 2>/dev/null || true
    
    echo "清理完成！"
    echo "请等待几分钟，然后重新安装监控脚本。"
else
    echo "请手动执行上述清理命令。"
fi
