#!/bin/bash

echo "=== 调试进程终止问题 ==="
echo

echo "当前脚本信息："
echo "脚本PID: $$"
echo "脚本名称: $0"
echo "脚本路径: $(realpath "$0")"

echo
echo "查找所有相关进程："
ps aux | grep -E "(cf-vps-monitor|vps-monitor|monitor)" | grep -v grep | while read -r line; do
    pid=$(echo "$line" | awk '{print $2}')
    ppid=$(echo "$line" | awk '{print $3}')
    cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
    echo "PID: $pid, PPID: $ppid, CMD: $cmd"
    
    if [[ "$pid" == "$$" ]]; then
        echo "  ↑ 这是当前脚本"
    elif [[ "$ppid" == "$$" ]]; then
        echo "  ↑ 这是当前脚本的子进程"
    fi
done

echo
echo "进程树："
pstree -p $$ 2>/dev/null || echo "pstree不可用"

echo
echo "测试安全的进程终止方法..."

# 创建一个临时文件来存储要终止的PID
temp_file="/tmp/pids_to_kill_$$"

# 查找要终止的进程，但排除当前脚本和其子进程
ps aux | grep -E "(cf-vps-monitor|vps-monitor|monitor)" | grep -v grep | while read -r line; do
    pid=$(echo "$line" | awk '{print $2}')
    ppid=$(echo "$line" | awk '{print $3}')
    cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
    
    # 排除当前脚本
    if [[ "$pid" == "$$" ]]; then
        echo "跳过当前脚本: PID $pid"
        continue
    fi
    
    # 排除当前脚本的直接子进程
    if [[ "$ppid" == "$$" ]]; then
        echo "跳过当前脚本的子进程: PID $pid"
        continue
    fi
    
    # 检查是否是监控相关进程
    if echo "$cmd" | grep -qE "(monitor\.sh|api/report|vps.*monitor)" && ! echo "$cmd" | grep -q "debug_process_issue"; then
        echo "标记要终止的进程: PID $pid, CMD: $cmd"
        echo "$pid" >> "$temp_file"
    fi
done

echo
if [[ -f "$temp_file" && -s "$temp_file" ]]; then
    echo "要终止的进程列表："
    cat "$temp_file"
    
    echo
    echo "开始终止进程..."
    while read -r pid; do
        if [[ -n "$pid" && "$pid" =~ ^[0-9]+$ ]]; then
            echo "终止进程: $pid"
            kill -TERM "$pid" 2>/dev/null || echo "无法终止进程 $pid"
        fi
    done < "$temp_file"
    
    echo "等待进程退出..."
    sleep 2
    
    echo "检查剩余进程..."
    while read -r pid; do
        if [[ -n "$pid" && "$pid" =~ ^[0-9]+$ ]]; then
            if kill -0 "$pid" 2>/dev/null; then
                echo "强制终止进程: $pid"
                kill -KILL "$pid" 2>/dev/null || echo "无法强制终止进程 $pid"
            fi
        fi
    done < "$temp_file"
    
    rm -f "$temp_file"
else
    echo "没有找到需要终止的进程"
fi

echo
echo "最终检查..."
echo "当前脚本是否还在运行: $$"
if kill -0 $$ 2>/dev/null; then
    echo "✅ 当前脚本仍在运行"
else
    echo "❌ 当前脚本已被终止"
fi

echo
echo "剩余相关进程："
ps aux | grep -E "(cf-vps-monitor|vps-monitor|monitor)" | grep -v grep | grep -v "debug_process_issue" || echo "没有剩余进程"

echo
echo "调试完成！"
