#!/bin/bash

# 验证VPS脚本修正的简单测试

echo "=== 验证VPS脚本修正 ==="
echo

# 1. 检查worker.js中的安装脚本是否包含uptime字段
echo "1. 检查worker.js安装脚本中的uptime字段..."
if grep -q '"uptime":' worker.js; then
    echo "✓ worker.js安装脚本已包含uptime字段"
else
    echo "✗ worker.js安装脚本缺少uptime字段"
fi

# 2. 检查worker.js中是否有get_uptime函数
echo "2. 检查worker.js中的get_uptime函数..."
if grep -q "get_uptime()" worker.js; then
    echo "✓ worker.js已包含get_uptime函数"
else
    echo "✗ worker.js缺少get_uptime函数"
fi

# 3. 检查worker.js中的内存函数是否改进
echo "3. 检查worker.js中的内存函数改进..."
if grep -q "MemAvailable\|buffers\|cached" worker.js; then
    echo "✓ worker.js内存函数已改进"
else
    echo "✗ worker.js内存函数未改进"
fi

# 4. 检查cf-vps-monitor.sh中的容器支持
echo "4. 检查cf-vps-monitor.sh中的容器支持..."
if grep -q "cgroup v2\|memory.max" cf-vps-monitor.sh; then
    echo "✓ cf-vps-monitor.sh已支持cgroup v2"
else
    echo "✗ cf-vps-monitor.sh缺少cgroup v2支持"
fi

# 5. 检查错误处理改进
echo "5. 检查错误处理改进..."
if grep -q "数据格式错误 - 请检查上报的数据格式" cf-vps-monitor.sh; then
    echo "✓ 错误处理已改进"
else
    echo "✗ 错误处理未改进"
fi

# 6. 检查数据验证改进
echo "6. 检查数据验证改进..."
if grep -q "警告.*数据无效，使用默认值" cf-vps-monitor.sh; then
    echo "✓ 数据验证已改进"
else
    echo "✗ 数据验证未改进"
fi

echo
echo "=== 验证完成 ==="

# 7. 语法检查
echo "7. 进行语法检查..."
if bash -n cf-vps-monitor.sh; then
    echo "✓ cf-vps-monitor.sh语法正确"
else
    echo "✗ cf-vps-monitor.sh语法错误"
fi

if command -v node >/dev/null 2>&1; then
    if node -c worker.js; then
        echo "✓ worker.js语法正确"
    else
        echo "✗ worker.js语法错误"
    fi
else
    echo "? 跳过worker.js语法检查（node未安装）"
fi

echo
echo "=== 修正验证总结 ==="
echo "主要修正内容："
echo "1. ✓ 在worker.js安装脚本中添加了uptime字段"
echo "2. ✓ 改进了worker.js中的内存计算函数"
echo "3. ✓ 增强了cf-vps-monitor.sh的容器环境支持"
echo "4. ✓ 改进了错误处理和数据验证"
echo "5. ✓ 统一了两个脚本的数据格式"
