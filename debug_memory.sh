#!/bin/bash

echo "=== 内存调试信息 ==="
echo

echo "1. /proc/meminfo 原始数据："
if [[ -f /proc/meminfo ]]; then
    grep -E "^(MemTotal|MemFree|MemAvailable|Buffers|Cached):" /proc/meminfo
else
    echo "/proc/meminfo 不存在"
fi

echo
echo "2. free 命令输出："
if command -v free >/dev/null 2>&1; then
    free -k
else
    echo "free 命令不可用"
fi

echo
echo "3. 计算过程："

if [[ -f /proc/meminfo ]]; then
    total=$(grep "^MemTotal:" /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "0")
    mem_free=$(grep "^MemFree:" /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "0")
    buffers=$(grep "^Buffers:" /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "0")
    cached=$(grep "^Cached:" /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "0")
    mem_available=$(grep "^MemAvailable:" /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "0")
    
    echo "MemTotal: $total KB"
    echo "MemFree: $mem_free KB"
    echo "Buffers: $buffers KB"
    echo "Cached: $cached KB"
    echo "MemAvailable: $mem_available KB"
    echo
    
    echo "计算方式1（使用MemAvailable）："
    if [[ "$mem_available" -gt 0 ]]; then
        used1=$((total - mem_available))
        free1="$mem_available"
        echo "used = total - MemAvailable = $total - $mem_available = $used1 KB"
        echo "free = MemAvailable = $free1 KB"
        echo "验证: used + free = $used1 + $free1 = $((used1 + free1)) KB (应该等于 $total KB)"
    else
        echo "MemAvailable 不可用"
    fi
    
    echo
    echo "计算方式2（手动计算）："
    available2=$((mem_free + buffers + cached))
    used2=$((total - available2))
    free2="$available2"
    echo "available = MemFree + Buffers + Cached = $mem_free + $buffers + $cached = $available2 KB"
    echo "used = total - available = $total - $available2 = $used2 KB"
    echo "free = available = $free2 KB"
    echo "验证: used + free = $used2 + $free2 = $((used2 + free2)) KB (应该等于 $total KB)"
    
    echo
    echo "转换为MB："
    echo "total: $(echo "scale=1; $total / 1024" | bc 2>/dev/null || echo "0") MB"
    if [[ "$mem_available" -gt 0 ]]; then
        echo "方式1 - used: $(echo "scale=1; $used1 / 1024" | bc 2>/dev/null || echo "0") MB"
        echo "方式1 - free: $(echo "scale=1; $free1 / 1024" | bc 2>/dev/null || echo "0") MB"
    fi
    echo "方式2 - used: $(echo "scale=1; $used2 / 1024" | bc 2>/dev/null || echo "0") MB"
    echo "方式2 - free: $(echo "scale=1; $free2 / 1024" | bc 2>/dev/null || echo "0") MB"
fi

echo
echo "4. 当前脚本的get_memory_usage函数输出："
# 从cf-vps-monitor.sh中提取get_memory_usage函数并执行
if [[ -f cf-vps-monitor.sh ]]; then
    # 提取必要的函数
    source <(grep -A 20 "^sanitize_integer()" cf-vps-monitor.sh)
    source <(grep -A 20 "^command_exists()" cf-vps-monitor.sh)
    
    # 设置环境变量
    OS=$(uname -s)
    CONTAINER_ENV="false"
    
    # 提取并执行get_memory_usage函数
    source <(sed -n '/^get_memory_usage()/,/^}/p' cf-vps-monitor.sh)
    
    echo "get_memory_usage() 输出："
    get_memory_usage
else
    echo "cf-vps-monitor.sh 不存在"
fi
