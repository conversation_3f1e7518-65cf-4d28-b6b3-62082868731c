#!/bin/bash

echo "=== 测试卸载功能的彻底性 ==="
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    if [[ "$status" == "OK" ]]; then
        echo -e "${GREEN}✓${NC} $message"
    elif [[ "$status" == "WARN" ]]; then
        echo -e "${YELLOW}⚠${NC} $message"
    else
        echo -e "${RED}✗${NC} $message"
    fi
}

echo "检查卸载后的系统状态..."
echo

# 1. 检查进程
echo "1. 检查相关进程："
monitor_processes=$(ps aux | grep -E "(cf-vps-monitor|vps.*monitor|monitor\.sh)" | grep -v grep | wc -l)
if [[ "$monitor_processes" -eq 0 ]]; then
    print_status "OK" "没有发现监控相关进程"
else
    print_status "FAIL" "发现 $monitor_processes 个监控相关进程:"
    ps aux | grep -E "(cf-vps-monitor|vps.*monitor|monitor\.sh)" | grep -v grep
fi

# 2. 检查systemd服务
echo
echo "2. 检查systemd服务："
if command -v systemctl >/dev/null 2>&1; then
    # 检查用户级服务
    user_services=$(systemctl --user list-units --all 2>/dev/null | grep -E "(monitor|vps)" | wc -l)
    if [[ "$user_services" -eq 0 ]]; then
        print_status "OK" "没有发现用户级监控服务"
    else
        print_status "WARN" "发现用户级监控服务:"
        systemctl --user list-units --all 2>/dev/null | grep -E "(monitor|vps)"
    fi
    
    # 检查系统级服务
    system_services=$(systemctl list-units --all 2>/dev/null | grep -E "(monitor|vps)" | wc -l)
    if [[ "$system_services" -eq 0 ]]; then
        print_status "OK" "没有发现系统级监控服务"
    else
        print_status "WARN" "发现系统级监控服务:"
        systemctl list-units --all 2>/dev/null | grep -E "(monitor|vps)"
    fi
else
    print_status "OK" "systemctl不可用，跳过检查"
fi

# 3. 检查配置目录
echo
echo "3. 检查配置目录："
config_dirs=(
    "$HOME/.cf-vps-monitor"
    "/root/.cf-vps-monitor"
    "/opt/cf-vps-monitor"
    "$HOME/cf-vps-monitor"
    "$HOME/vps-monitor"
)

found_dirs=0
for dir in "${config_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
        print_status "FAIL" "发现残留目录: $dir"
        found_dirs=$((found_dirs + 1))
    fi
done

if [[ "$found_dirs" -eq 0 ]]; then
    print_status "OK" "没有发现残留配置目录"
fi

# 4. 检查可执行文件
echo
echo "4. 检查可执行文件："
exec_files=(
    "/usr/local/bin/cf-vps-monitor"
    "/usr/local/bin/vps-monitor"
    "/usr/local/bin/monitor.sh"
)

found_files=0
for file in "${exec_files[@]}"; do
    if [[ -f "$file" ]]; then
        print_status "FAIL" "发现残留文件: $file"
        found_files=$((found_files + 1))
    fi
done

if [[ "$found_files" -eq 0 ]]; then
    print_status "OK" "没有发现残留可执行文件"
fi

# 5. 检查临时文件
echo
echo "5. 检查临时文件："
temp_files=$(find /tmp -name "*monitor*" -o -name "*vps*" 2>/dev/null | wc -l)
if [[ "$temp_files" -eq 0 ]]; then
    print_status "OK" "没有发现临时文件"
else
    print_status "WARN" "发现 $temp_files 个临时文件:"
    find /tmp -name "*monitor*" -o -name "*vps*" 2>/dev/null
fi

# 6. 检查cron任务
echo
echo "6. 检查cron任务："
if crontab -l 2>/dev/null | grep -E "(monitor|vps|cf-)" >/dev/null 2>&1; then
    print_status "WARN" "发现可能的cron任务:"
    crontab -l 2>/dev/null | grep -E "(monitor|vps|cf-)"
else
    print_status "OK" "没有发现相关cron任务"
fi

# 7. 检查网络连接
echo
echo "7. 检查网络连接："
network_connections=$(netstat -an 2>/dev/null | grep -E "(api/report|worker)" | wc -l)
if [[ "$network_connections" -eq 0 ]]; then
    print_status "OK" "没有发现相关网络连接"
else
    print_status "WARN" "发现可能的网络连接:"
    netstat -an 2>/dev/null | grep -E "(api/report|worker)"
fi

echo
echo "=== 卸载完整性总结 ==="

# 计算总体状态
total_issues=0
if [[ "$monitor_processes" -gt 0 ]]; then total_issues=$((total_issues + 1)); fi
if [[ "$found_dirs" -gt 0 ]]; then total_issues=$((total_issues + 1)); fi
if [[ "$found_files" -gt 0 ]]; then total_issues=$((total_issues + 1)); fi

if [[ "$total_issues" -eq 0 ]]; then
    print_status "OK" "卸载非常彻底，没有发现重要残留"
elif [[ "$total_issues" -le 2 ]]; then
    print_status "WARN" "卸载基本彻底，发现少量残留"
else
    print_status "FAIL" "卸载不够彻底，发现多处残留"
fi

echo
echo "建议："
echo "1. 如果发现进程残留，请手动终止: pkill -f monitor"
echo "2. 如果发现目录残留，请手动删除: rm -rf <目录>"
echo "3. 如果发现服务残留，请手动清理systemd服务"
echo "4. 重启系统可以确保完全清理"
