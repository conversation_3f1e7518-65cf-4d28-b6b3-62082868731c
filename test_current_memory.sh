#!/bin/bash

echo "=== 测试当前内存函数输出 ==="
echo

# 设置环境变量
export OS=$(uname -s)
export CONTAINER_ENV="false"

# 从cf-vps-monitor.sh中提取必要的函数
source <(grep -A 20 "^sanitize_integer()" cf-vps-monitor.sh)
source <(grep -A 20 "^command_exists()" cf-vps-monitor.sh)

# 提取get_memory_usage函数
source <(sed -n '/^get_memory_usage()/,/^}/p' cf-vps-monitor.sh)

echo "1. 直接调用get_memory_usage():"
memory_result=$(get_memory_usage)
echo "$memory_result"

echo
echo "2. 解析JSON数据:"
if command -v python3 >/dev/null 2>&1; then
    echo "$memory_result" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    total_kb = data['total']
    used_kb = data['used']
    free_kb = data['free']
    
    total_mb = total_kb / 1024
    used_mb = used_kb / 1024
    free_mb = free_kb / 1024
    
    print(f'总计: {total_mb:.1f} MB ({total_kb} KB)')
    print(f'已用: {used_mb:.1f} MB ({used_kb} KB)')
    print(f'空闲: {free_mb:.1f} MB ({free_kb} KB)')
    print(f'验证: {used_mb:.1f} + {free_mb:.1f} = {used_mb + free_mb:.1f} MB')
    print(f'是否相等: {abs(total_mb - (used_mb + free_mb)) < 0.1}')
except Exception as e:
    print(f'解析错误: {e}')
"
else
    echo "python3不可用，无法解析JSON"
fi

echo
echo "3. 检查是否有其他内存函数:"
if grep -q "get_memory_usage" worker.js; then
    echo "worker.js中也有get_memory_usage函数"
fi

echo
echo "4. 检查当前系统内存信息:"
echo "MemTotal: $(grep MemTotal /proc/meminfo)"
echo "MemFree: $(grep MemFree /proc/meminfo)"

echo
echo "5. 检查是否在容器中:"
if [[ -f /.dockerenv ]]; then
    echo "检测到Docker容器环境"
elif [[ -f /proc/version ]] && grep -q Microsoft /proc/version; then
    echo "检测到WSL环境"
elif [[ -f /sys/fs/cgroup/memory/memory.limit_in_bytes ]]; then
    echo "检测到cgroup v1容器环境"
    echo "内存限制: $(cat /sys/fs/cgroup/memory/memory.limit_in_bytes)"
elif [[ -f /sys/fs/cgroup/memory.max ]]; then
    echo "检测到cgroup v2容器环境"
    echo "内存限制: $(cat /sys/fs/cgroup/memory.max)"
else
    echo "普通Linux环境"
fi
