#!/bin/bash

echo "=== 测试数据上报 ==="
echo

# 检查是否有配置文件
CONFIG_FILE="$HOME/.cf-vps-monitor/config"
if [[ -f "$CONFIG_FILE" ]]; then
    echo "找到配置文件: $CONFIG_FILE"
    source "$CONFIG_FILE"
    echo "WORKER_URL: $WORKER_URL"
    echo "SERVER_ID: $SERVER_ID"
    echo "API_KEY: ${API_KEY:0:10}..."
else
    echo "没有找到配置文件，请手动输入配置："
    read -p "Worker URL: " WORKER_URL
    read -p "Server ID: " SERVER_ID
    read -p "API Key: " API_KEY
fi

echo
echo "准备上报测试数据..."

# 设置环境变量
export OS=$(uname -s)
export CONTAINER_ENV="false"

# 从cf-vps-monitor.sh中提取必要的函数
source <(grep -A 20 "^sanitize_integer()" cf-vps-monitor.sh)
source <(grep -A 20 "^command_exists()" cf-vps-monitor.sh)

# 提取各个数据收集函数
source <(sed -n '/^get_memory_usage()/,/^}/p' cf-vps-monitor.sh)
source <(sed -n '/^get_cpu_usage()/,/^}/p' cf-vps-monitor.sh)
source <(sed -n '/^get_disk_usage()/,/^}/p' cf-vps-monitor.sh)
source <(sed -n '/^get_network_usage()/,/^}/p' cf-vps-monitor.sh)
source <(sed -n '/^get_uptime()/,/^}/p' cf-vps-monitor.sh)

# 收集数据
echo "收集系统数据..."
timestamp=$(date +%s)
cpu=$(get_cpu_usage)
memory=$(get_memory_usage)
disk=$(get_disk_usage)
network=$(get_network_usage)
uptime=$(get_uptime)

echo "收集到的数据："
echo "CPU: $cpu"
echo "Memory: $memory"
echo "Disk: $disk"
echo "Network: $network"
echo "Uptime: $uptime"

# 构建JSON数据
data="{\"timestamp\":$timestamp,\"cpu\":$cpu,\"memory\":$memory,\"disk\":$disk,\"network\":$network,\"uptime\":$uptime}"

echo
echo "构建的JSON数据："
echo "$data"

echo
echo "开始上报数据..."

# 上报数据
response=$(curl -s -w "%{http_code}" -X POST "$WORKER_URL/api/report/$SERVER_ID" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: $API_KEY" \
    -d "$data" 2>/dev/null || echo "000")

http_code="${response: -3}"
response_body="${response%???}"

echo "HTTP状态码: $http_code"
echo "响应内容: $response_body"

if [[ "$http_code" == "200" ]]; then
    echo "✅ 数据上报成功！"
    echo "请检查前端是否更新了数据。"
    
    # 解析内存数据并显示
    if command -v python3 >/dev/null 2>&1; then
        echo
        echo "解析内存数据："
        echo "$memory" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    total_mb = data['total'] / 1024
    used_mb = data['used'] / 1024
    free_mb = data['free'] / 1024
    usage_percent = data['usage_percent']
    
    print(f'总计: {total_mb:.1f} MB')
    print(f'已用: {used_mb:.1f} MB')
    print(f'空闲: {free_mb:.1f} MB')
    print(f'使用率: {usage_percent}%')
    print(f'验证: {used_mb:.1f} + {free_mb:.1f} = {used_mb + free_mb:.1f} MB')
except Exception as e:
    print(f'解析错误: {e}')
"
    fi
else
    echo "❌ 数据上报失败！"
    case "$http_code" in
        "400") echo "数据格式错误" ;;
        "401") echo "认证失败 - 请检查API密钥" ;;
        "404") echo "服务器不存在 - 请检查服务器ID" ;;
        "429") echo "请求过于频繁" ;;
        "500"|"503") echo "服务器错误" ;;
        "000") echo "网络连接失败" ;;
        *) echo "未知错误" ;;
    esac
fi

echo
echo "=== 调试信息 ==="
echo "如果前端仍然显示错误的数据，可能的原因："
echo "1. 前端缓存了旧数据，需要等待或刷新"
echo "2. 有多个服务器在上报数据，前端显示的是其他服务器的数据"
echo "3. 前端连接的是不同的Worker URL"
echo "4. 服务器ID不匹配"

echo
echo "建议检查："
echo "1. 在前端查看是否有多个服务器"
echo "2. 确认当前服务器ID是否正确"
echo "3. 等待几分钟后刷新前端页面"
