#!/bin/bash

echo "=== 测试安全卸载功能 ==="
echo

# 创建模拟监控进程
echo "创建模拟监控进程..."

# 创建模拟脚本
cat > /tmp/fake_vps_monitor.sh << 'EOF'
#!/bin/bash
echo "Fake VPS monitor starting..."
while true; do
    echo "Monitoring... $(date)"
    sleep 5
done
EOF

chmod +x /tmp/fake_vps_monitor.sh

# 启动模拟进程
nohup bash /tmp/fake_vps_monitor.sh > /tmp/fake_monitor.log 2>&1 &
fake_pid1=$!

nohup bash -c 'while true; do echo "API report $(date)"; sleep 10; done' > /tmp/fake_api.log 2>&1 &
fake_pid2=$!

echo "启动了模拟进程:"
echo "  VPS Monitor: PID $fake_pid1"
echo "  API Report: PID $fake_pid2"

sleep 1

echo
echo "当前相关进程:"
ps aux | grep -E "(monitor|api)" | grep -v grep

echo
echo "=== 测试新的安全终止逻辑 ==="

# 模拟新的卸载逻辑
current_pid=$$
temp_pid_file="/tmp/monitor_pids_to_kill_$$"

echo "当前脚本PID: $current_pid"

# 查找需要终止的进程
ps aux | grep -E "(monitor|api)" | grep -v grep | grep -v "$current_pid" > "$temp_pid_file" 2>/dev/null || true

if [[ -f "$temp_pid_file" && -s "$temp_pid_file" ]]; then
    echo "发现需要终止的进程:"
    cat "$temp_pid_file"
    
    echo
    echo "第一轮：优雅终止"
    while read -r line; do
        if [[ -n "$line" ]]; then
            local pid=$(echo "$line" | awk '{print $2}')
            local cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
            
            if [[ "$pid" =~ ^[0-9]+$ && "$pid" != "$current_pid" ]]; then
                if ! echo "$cmd" | grep -q "test_safe_uninstall"; then
                    echo "优雅终止进程: $pid ($cmd)"
                    kill -TERM "$pid" 2>/dev/null || true
                fi
            fi
        fi
    done < "$temp_pid_file"
    
    echo "等待进程退出..."
    sleep 3
    
    echo "第二轮：强制终止"
    while read -r line; do
        if [[ -n "$line" ]]; then
            local pid=$(echo "$line" | awk '{print $2}')
            local cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
            
            if [[ "$pid" =~ ^[0-9]+$ && "$pid" != "$current_pid" ]]; then
                if kill -0 "$pid" 2>/dev/null; then
                    if ! echo "$cmd" | grep -q "test_safe_uninstall"; then
                        echo "强制终止进程: $pid ($cmd)"
                        kill -KILL "$pid" 2>/dev/null || true
                    fi
                fi
            fi
        fi
    done < "$temp_pid_file"
    
    rm -f "$temp_pid_file"
else
    echo "没有发现需要终止的进程"
fi

echo
echo "最终检查:"
echo "当前脚本是否还在运行: $$"
if kill -0 $$ 2>/dev/null; then
    echo "✅ 当前脚本仍在运行"
else
    echo "❌ 当前脚本已被终止"
fi

echo
echo "剩余相关进程:"
remaining=$(ps aux | grep -E "(monitor|api)" | grep -v grep | grep -v "test_safe_uninstall" | wc -l)
if [[ "$remaining" -eq 0 ]]; then
    echo "✅ 所有目标进程已被终止"
else
    echo "❌ 仍有 $remaining 个进程在运行:"
    ps aux | grep -E "(monitor|api)" | grep -v grep | grep -v "test_safe_uninstall"
fi

echo
echo "清理测试文件..."
rm -f /tmp/fake_vps_monitor.sh /tmp/fake_monitor.log /tmp/fake_api.log

echo "✅ 测试完成!"
echo
echo "关键改进:"
echo "1. 使用临时文件避免子shell问题"
echo "2. 更严格的进程过滤逻辑"
echo "3. 排除当前卸载脚本"
echo "4. 两轮终止策略：优雅 + 强制"
