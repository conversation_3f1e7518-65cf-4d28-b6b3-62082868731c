#!/bin/bash

echo "=== 测试修正后的卸载功能 ==="
echo

# 模拟一些监控进程（用于测试）
echo "创建测试进程..."

# 创建一个模拟的监控脚本
cat > /tmp/fake_monitor.sh << 'EOF'
#!/bin/bash
while true; do
    echo "Fake monitor running..."
    sleep 5
done
EOF

chmod +x /tmp/fake_monitor.sh

# 启动模拟进程
nohup bash /tmp/fake_monitor.sh > /dev/null 2>&1 &
fake_pid=$!
echo "启动了模拟监控进程，PID: $fake_pid"

# 创建一个模拟的API上报进程
nohup bash -c 'while true; do curl -s http://example.com/api/report >/dev/null 2>&1 || true; sleep 10; done' > /dev/null 2>&1 &
api_pid=$!
echo "启动了模拟API上报进程，PID: $api_pid"

echo
echo "当前相关进程："
ps aux | grep -E "(monitor|api/report)" | grep -v grep

echo
echo "现在测试卸载功能..."
echo "注意：这只是测试进程终止逻辑，不会执行完整卸载"

# 模拟卸载函数中的进程终止部分
echo
echo "=== 模拟卸载进程终止逻辑 ==="

current_pid=$$
echo "当前脚本PID: $current_pid"

echo "查找需要终止的进程..."
ps aux | grep -E "(monitor|api/report)" | grep -v grep | grep -v "$$" | while read -r line; do
    pid=$(echo "$line" | awk '{print $2}')
    cmd=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}')
    echo "发现进程: PID=$pid, CMD=$cmd"
    
    if [[ -n "$pid" && "$pid" != "$current_pid" ]]; then
        echo "终止进程: $pid"
        kill -TERM "$pid" 2>/dev/null || true
    fi
done

echo
echo "等待进程退出..."
sleep 2

echo "检查剩余进程..."
remaining=$(ps aux | grep -E "(monitor|api/report)" | grep -v grep | grep -v "$$" | wc -l)
echo "剩余相关进程数: $remaining"

if [[ "$remaining" -gt 0 ]]; then
    echo "强制终止剩余进程..."
    ps aux | grep -E "(monitor|api/report)" | grep -v grep | grep -v "$$" | while read -r line; do
        pid=$(echo "$line" | awk '{print $2}')
        if [[ -n "$pid" && "$pid" != "$current_pid" ]]; then
            echo "强制终止进程: $pid"
            kill -KILL "$pid" 2>/dev/null || true
        fi
    done
fi

echo
echo "最终检查..."
sleep 1
final_remaining=$(ps aux | grep -E "(monitor|api/report)" | grep -v grep | grep -v "$$" | wc -l)
echo "最终剩余进程数: $final_remaining"

if [[ "$final_remaining" -eq 0 ]]; then
    echo "✅ 进程终止测试成功！"
else
    echo "❌ 仍有进程未终止"
    ps aux | grep -E "(monitor|api/report)" | grep -v grep | grep -v "$$"
fi

echo
echo "清理测试文件..."
rm -f /tmp/fake_monitor.sh

echo "✅ 测试完成！"
echo
echo "修正说明："
echo "1. 使用 ps + grep + awk 替代 pkill，更精确控制"
echo "2. 排除当前脚本PID ($$)，避免自杀"
echo "3. 先发送TERM信号，再发送KILL信号"
echo "4. 详细的日志输出，便于调试"
